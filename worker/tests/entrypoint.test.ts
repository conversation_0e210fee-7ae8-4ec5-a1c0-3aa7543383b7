import { test, mock, beforeEach, after } from 'node:test';
import assert from 'node:assert';
import { PlatformAwareGameEntrypoint } from '../src/entrypoint';
import redis from '../src/redis';
import * as shared from 'shared';
import { JobType, LoginError, PlatformType, AppType } from 'shared';

// Helpers
const baseUser = {
  platformId: PlatformType.WPTGO,
  userId: 123,
  username: 'u',
  password: 'p',
  deviceId: 'd',
  appId: AppType.MTT,
};

function makePlatformUser(overrides: Partial<shared.PlatformUserData> = {}): shared.PlatformUserData {
  return { userId: baseUser.userId, token: 'plat-token', deviceId: baseUser.deviceId, ...overrides };
}

function makeMttUser(overrides: Partial<shared.MttUserData> = {}): shared.MttUserData {
  return { mtt: { token: 'mtt-token' }, user: { nickname: 'nick' }, ...overrides } as shared.MttUserData;
}

function makeAdapters(options: { isMtt: boolean; balanceTickets?: shared.Ticket[] } = { isMtt: true }) {
  const gameAdapter: any = {
    init: () => Promise.resolve(),
    play: mock.fn(async () => {}),
    scan: mock.fn(async () => {}),
    check: mock.fn(async () => {}),
    balance: mock.fn(
        async () =>
            options.balanceTickets ?? [
                { toolId: 11, ticketId: 111 },
                { toolId: 22, ticketId: 222 },
            ],
    ),
    stop: mock.fn(async () => {}),
    isMtt: () => options.isMtt,
  };

  const platformAdapter: any = {
    init: () => {},
    login: mock.fn(async () => makePlatformUser()),
    resolveMttUserData: mock.fn(async () => makeMttUser()),
    balance: mock.fn(async () => ({ diamond: 0, gold: 0, usdt: 0, usd: 0 } as shared.Balance)),
    transfer: mock.fn(async () => {}),
  };

  return { gameAdapter, platformAdapter };
}

// Reset spies before each test
beforeEach(() => {
  mock.reset();
  // Replace redis methods with spies
  mock.method(redis, 'store', mock.fn(async () => {}));
  mock.method(redis, 'fetch', mock.fn(async () => null));
  mock.method(redis, 'remove', mock.fn(async () => {}));
});

// Ensure we close any open handles to avoid hanging test runner
after(async () => {
  try {
    if ((redis.workerQueue as any)?.close) {
      await (redis.workerQueue as any).close();
    }
  } catch { /* empty */ }
  try {
    if ((redis.connection as any)?.quit) {
      await (redis.connection as any).quit();
    } else if ((redis.connection as any)?.disconnect) {
      (redis.connection as any).disconnect();
    }
  } catch { /* empty */ }
});

function onMessageCollector() {
  const messages: any[] = [];
  const handler = (data: any) => messages.push(data);
  return { handler, messages };
}

// 1) ensurePlatformUser caches after login and uses cache next time
test('ensurePlatformUser logs in when no cache, stores, and uses cache next time', async () => {
  const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: false });
  const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

  const storeSpy = (redis.store as any);
  const fetchSpy = (redis.fetch as any);

  // First call: no cache; emulate storing into a map
  const map = new Map<string, string | null>();
  fetchSpy.mock.mockImplementation(async (key: string) => map.get(key) ?? null);
  storeSpy.mock.mockImplementation(async (key: string, value: string) => { map.set(key, value); });

  const { handler, messages } = onMessageCollector();
  await entry.start(baseUser as any, JobType.BALANCE, 0, baseUser as any, handler);

  assert.strictEqual(platformAdapter.login.mock.calls.length, 1);
  assert.ok(storeSpy.mock.calls.length > 0);

  // Second call: should use cache, no additional login
  await entry.start(baseUser as any, JobType.BALANCE, 0, baseUser as any, handler);
  assert.strictEqual(platformAdapter.login.mock.calls.length, 1);
  assert.ok(messages.every((m) => m.balance));
});

// 2) resolveUserData uses MTT cache and skips login
test('resolveUserData (MTT) returns cached mtt-user and skips login', async () => {
  const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: true });
  const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

  const fetchSpy = (redis.fetch as any);

  // Preload mtt cache
  const mttCacheKey = `mtt-user:${baseUser.platformId}:${baseUser.userId}`;
  const cache = new Map<string, string | null>([[mttCacheKey, JSON.stringify(makeMttUser())]]);
  fetchSpy.mock.mockImplementation(async (key: string) => cache.get(key) ?? null);

  const { handler } = onMessageCollector();
  await entry.start(baseUser as any, JobType.CHECK, 42, baseUser as any, handler);

  assert.strictEqual(platformAdapter.login.mock.calls.length, 0);
  assert.strictEqual(gameAdapter.check.mock.calls.length, 1);
});

// 3) resolveUserData throws LoginError when no MTT token
test('resolveUserData throws LoginError when resolveMttUserData returns no token', async () => {
  const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: true });
  const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

  platformAdapter.resolveMttUserData.mock.mockImplementation(async () => ({ mtt: {}, user: { nickname: 'n' } }));

  await assert.rejects(
    () => entry.start(baseUser as any, JobType.CHECK, 1, baseUser as any, () => {}),
    (err: any) => err instanceof LoginError
  );
});

// 4) BALANCE: with non-MTT adapter, tickets not fetched
test('BALANCE: does not fetch tickets for non-MTT adapters', async () => {
  const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: false });
  const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

  const { handler, messages } = onMessageCollector();
  await entry.start(baseUser as any, JobType.BALANCE, 0, baseUser as any, handler);

  // Should not call gameAdapter.balance when isMtt is false
  assert.strictEqual(gameAdapter.balance.mock.calls.length, 0);
  assert.strictEqual(messages.length, 1);
  assert.ok(messages[0].balance);
  assert.strictEqual(messages[0].balance.tickets, undefined);
});

// 5) BALANCE: with feature enabled by default and MTT adapter, fetch tickets and include in response
test('BALANCE: fetches tickets via gameAdapter for MTT adapters', async () => {
    mock.method(
        PlatformAwareGameEntrypoint.prototype,
        'isFetchTickets',
        mock.fn(async () => true),
    );

    const expectedTickets = [
        { toolId: 7, ticketId: 107 },
        { toolId: 8, ticketId: 108 },
        { toolId: 9, ticketId: 109 },
    ];
    const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: true, balanceTickets: expectedTickets });
    const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

    const { handler, messages } = onMessageCollector();
    await entry.start(baseUser as any, JobType.BALANCE, 0, baseUser as any, handler);

    assert.strictEqual(gameAdapter.balance.mock.calls.length, 1);
    assert.deepStrictEqual(messages[0].balance.tickets, expectedTickets);
});

// 6) On LoginError during start, clearCache is called (both keys removed)
test('clears cache on LoginError from platformAdapter.login', async () => {
  const { gameAdapter, platformAdapter } = makeAdapters({ isMtt: true });
  const entry = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

  const removeSpy = (redis.remove as any);

  platformAdapter.login.mock.mockImplementation(async () => { throw new LoginError('bad'); });

  await assert.rejects(
    () => entry.start(baseUser as any, JobType.SCAN, 0, baseUser as any, () => {}),
    (err: any) => err instanceof LoginError,
  );

  const userKey = `user:${baseUser.platformId}:${baseUser.userId}`;
  const mttKey = `mtt-user:${baseUser.platformId}:${baseUser.userId}`;
  const removedKeys = removeSpy.mock.calls.map((c: any) => c.arguments[0]);
  assert.ok(removedKeys.includes(userKey));
  assert.ok(removedKeys.includes(mttKey));
});
