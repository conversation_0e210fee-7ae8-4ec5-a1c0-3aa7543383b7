import { RobotHttpReq } from 'pkw';

import { MttUserData, Balance, UserData, PlatformUserData, PkwUserData } from 'shared';
import { ManagingPlatformAdapter } from '../models/model';
import { RWPK_URL } from '../config';

const IS_PHONE_LOGIN = true;
const IS_SIGNED = true;

class RWpkAdapter implements ManagingPlatformAdapter {

    init() {
        RobotHttpReq.WPK_HTTP_URL = RWPK_URL;
    }

    async login(user: UserData): Promise<PkwUserData> {
        const userData = await RobotHttpReq.wpkLogin(user, IS_PHONE_LOGIN, IS_SIGNED);
        return {
            userId: userData.user.userId,
            token: userData.sessionToken,
            deviceId: user.deviceId,
            user: {
                nickname: userData.user.nickname,
            },
        } as PkwUserData;
    }

    async resolveMttUserData(platformUser: PlatformUserData): Promise<MttUserData> {
        const response = await RobotHttpReq.wpkRequest(
            platformUser,
            '/mtt/authApi',
            {},
            IS_SIGNED,
        );
        const nickname = (platformUser as PkwUserData).user.nickname!;
        return { mtt: { token: response.data.token }, user: { nickname } };
    }

    async balance(platformUser: PlatformUserData) {
        const response: any = await RobotHttpReq.wpkRequest(
            platformUser,
            '/userWallet/getWalletPageInfo',
            {},
            IS_SIGNED,
        );
        if (response?.data?.walletInfo?.amount == null) {
            throw new Error('Invalid wallet response format');
        }
        const wallet = response.data.walletInfo;

        return {
            diamond: 0,
            gold: wallet.amount,
            usdt: 0,
        } as Balance;
    }

    // eslint-disable-next-line @typescript-eslint/require-await
    async transfer() {
        throw new Error('Method not implemented.');
    }
}

export const rwpkPlatform = new RWpkAdapter();
