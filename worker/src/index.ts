import dotenv from 'dotenv';
import process from 'node:process';
import fs from 'fs';
// we need to update ENVs as soon as possible, because other modules will use them as well
dotenv.config();
if (process.env.NODE_ENV) {
    dotenv.config({ path: `.env.${process.env.NODE_ENV}`, override: true });
}
if (fs.existsSync('private.env')) {
    dotenv.config({ path: `private.env`, override: true });
}

import { Queue } from 'bullmq';
import Arena from 'bull-arena';
import client from 'prom-client';

import { initMongoDBConnection } from './mongo';
import redis from './redis';
import { initFastify } from './fastify';
import { initializeWorker } from './worker';
import { version } from '../package.json';
//
void (async function main() {
    console.log(`Starting Worker Service - version: ${version}`);

    // Initialize telemetry
    client.collectDefaultMetrics();

    // Initialing mongo DB
    console.log('Initializing MongoDB connection ....');
    await initMongoDBConnection();
    console.log('Initializing MongoDB connection .... DONE');

    // Initialing Arena instance
    const arenaInstance = Arena(
        {
            BullMQ: Queue,
            queues: [{ type: 'bullmq', name: redis.QUEUE_NAME, hostId: 'worker', redis: redis.connection }],
        },
        {
            basePath: '/',
            disableListen: true,
        },
    );

    // Starting Fastify server
    console.log('Initializing Fastify server ....');
    await initFastify(arenaInstance);

    const worker = initializeWorker();

    process.on('SIGTERM', async () => {
        console.log('Received SIGTERM. Stopping job intake...');
        await worker.close();
        console.log('All jobs complete. Exiting.');
        process.exit(0);
    });
})();
