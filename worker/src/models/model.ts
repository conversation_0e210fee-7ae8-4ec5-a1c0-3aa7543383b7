import { <PERSON>T<PERSON>, <PERSON>Data, JobDataHandler, MttUserData, Balance, UserData, PlatformUserData } from 'shared';
import { CurrencyType } from '../types';

export interface TransferJobData extends JobData {
    currency: CurrencyType;
    transferAmount: number; // amount to transfer to receiver
    receiverId: number; // userId of the receiver for transfer
    receiverUsername?: string; // username of the receiver for transfer
}

export interface GamePlatformAdapter {
    init(): Promise<void>;
    play(user: any, tableId: number, params: JobData, handler: JobDataHandler): Promise<void>;
    scan(user: any, params: JobData, handler: <PERSON>DataHand<PERSON>): Promise<void>;
    check(user: any, tableId: number, handler: <PERSON>DataHand<PERSON>): Promise<void>;
    balance(user: any): Promise<any>;
    stop(action: JobType): Promise<void>;
    isMtt(): boolean;
}

export interface ManagingPlatformAdapter {
    init: () => void;
    login: (user: UserData) => Promise<PlatformUserData>;
    resolveMttUserData: (platformUser: PlatformUserData) => Promise<MttUserData>;
    balance: (platformUser: PlatformUserData) => Promise<Balance>;
    transfer: (platformUser: PlatformUserData, params: TransferJobData) => Promise<void>;
}
