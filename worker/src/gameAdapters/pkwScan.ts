import { UnrecoverableError } from 'bullmq';
import {
    AppType,
    CurrencyType,
    JobData,
    JobDataHandler,
    JobType,
    logging,
    PkwUserData,
    TableData,
} from 'shared';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';
import { pkwConfig } from './pkwConfig';
import { LobbyDataFunction } from 'pkw/dist/pkw_ts/pkwRoom';

class PkwScanAdapter implements GamePlatformAdapter {
    getTablesProcessor(onMessage: JobDataHandler, supportedAppTypes: AppType[]): LobbyDataFunction {
        return (data: TableData[]) => {
            const tables: TableData[] = [];
            for (const table of data) {
                const appId = supportedAppTypes.find((appId) => {
                    const config = pkwConfig[appId];
                    return (
                        table.gameId === config.gameId &&
                        table.gameMode === config.gameMode &&
                        config.currencies.includes(table.currency as CurrencyType)
                    );
                });

                if (appId) {
                    tables.push({
                        ...table,
                        appId,
                    });
                }
            }
            onMessage({ tables });
        };
    }

    async init(): Promise<void> {
        await PkwMain.initScan();
    }

    play(): Promise<void> {
        throw new UnrecoverableError('Unsupported game platform action: play');
    }

    scan(user: PkwUserData, params: JobData, handler: JobDataHandler): Promise<void> {
        const supportedAppTypes = params.appIds || [];
        logging.info('PkwScanAdapter.start', { supportedAppTypes: supportedAppTypes });
        return PkwMain.pkwScan(user, this.getTablesProcessor(handler, supportedAppTypes));
    }

    check(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: check`);
    }

    balance(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: balance`);
    }

    stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Scan Adapter] stop', jobType);
        return Promise.resolve();
    }

    isMtt(): boolean {
        return false;
    }
}

export const pkwScanAdapter = new PkwScanAdapter();
