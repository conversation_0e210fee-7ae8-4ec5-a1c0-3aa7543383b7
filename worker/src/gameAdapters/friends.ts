import { <PERSON><PERSON><PERSON>, JobDataHandler, logging, PlatformUserData } from 'shared';
import { GamePlatformAdapter } from '../models/model';
import { CreateFriendsRoomParams, FriendsMain } from 'friends';
import { UnrecoverableError } from 'bullmq';

class FriendsAdapter implements GamePlatformAdapter {
    init(): Promise<void> {
        const urlConfig = {
            wpkHttpURL: process.env.WPK_URL!,
            wpkGameWsURL: process.env.WPK_URL!.replace('http', 'ws'), // tmp for development
        };
        FriendsMain.init(urlConfig);
        return Promise.resolve();
    }

    async play(user: PlatformUserData, tableId: number, params: JobData, handler: <PERSON>DataHandler): Promise<void> {
        await FriendsMain.play(
            user,
            tableId,
            params.profileName,
            handler,
            params.createRoomParams as CreateFriendsRoomParams,
        );
    }

    async scan(user: PlatformUserData, params: JobD<PERSON>, handler: <PERSON><PERSON>ata<PERSON><PERSON><PERSON>): Promise<void> {
        if (!params.clubId) {
            logging.error('Friends adapter requires clubId in params', { jobData: params });
            throw new UnrecoverableError('Friends adapter requires clubId');
        }
        await FriendsMain.scan(user, params.clubId, handler);
    }

    check(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: check`);
    }

    balance(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: balance`);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    stop(_: string): Promise<void> {
        setTimeout(() => {
            throw new Error('Friends adapter stop timeout reached, forcibly stopping the game.');
        }, 10_000);
        return FriendsMain.stop();
    }

    isMtt(): boolean {
        return false;
    }
}

export const friendsAdapter = new FriendsAdapter();
