import { <PERSON>Data, JobDataHandler, JobType, LoginError, MttConfigType, MttUserData, Ticket } from 'shared';
import { MttMain } from 'mtt';
import { MTT_CONFIG_URL, MTT_DEFAULT_CONFIG, RMTT_CONFIG_URL, RMTT_DEFAULT_CONFIG } from '../config';
import { GamePlatformAdapter } from '../models/model';
import { getMttConfig } from '../providers/mttConfigProvider';
import redis from '../redis';

class MttAdapterClass implements GamePlatformAdapter {
    private readonly configUrl: string;
    private readonly defaultConfig: MttConfigType;
    private readonly protoVersion: string;
    private readonly tag: string;

    constructor(configUrl: string, defaultConfig: MttConfigType, protoVersion: string, tag: string) {
        this.configUrl = configUrl;
        this.defaultConfig = defaultConfig;
        this.protoVersion = protoVersion;
        this.tag = tag;
    }

    async init(): Promise<void> {
        const envMttConfig = await getMttConfig(this.configUrl, this.defaultConfig, this.tag);
        MttMain.init(
            (id: number, data: any) => {
                void redis.store(`${this.tag}:${id}`, JSON.stringify(data));
            },
            async (id: number) => {
                const result = await redis.fetch(`${this.tag}:${id}`);
                if (result) {
                    return JSON.parse(result);
                }
                return null;
            },
            this.protoVersion,
            envMttConfig,
        );
    }

    private checkToken(user: MttUserData) {
        if (!user.mtt?.token) {
            throw new LoginError('MTT token not found in userData');
        }
    }

    async play(user: MttUserData, tableId: number, params: JobData, handler: JobDataHandler): Promise<void> {
        this.checkToken(user);
        const ticketId = params.ticketId ? Number(params.ticketId) : 0;
        return MttMain.play(user.mtt.token, tableId, ticketId, handler, params.profileName);
    }

    scan(user: MttUserData, _: JobData, handler: JobDataHandler): Promise<void> {
        this.checkToken(user);
        return MttMain.scan(user.mtt.token, handler);
    }

    check(user: MttUserData, tableId: number, handler: JobDataHandler): Promise<void> {
        this.checkToken(user);
        return MttMain.check(user.mtt.token, tableId, user?.user?.nickname, handler);
    }

    balance(user: MttUserData): Promise<Ticket[]> {
        this.checkToken(user);
        return MttMain.balance(user.mtt.token);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async stop(_: JobType) {}

    isMtt(): boolean {
        return true;
    }
}

export const mttAdapter = new MttAdapterClass(
    MTT_CONFIG_URL,
    MTT_DEFAULT_CONFIG,
    process.env.MTT_PROTOBUF_VERSION || 'v2',
    'mtt',
);
export const rmttAdapter = new MttAdapterClass(
    RMTT_CONFIG_URL,
    RMTT_DEFAULT_CONFIG,
    process.env.RMTT_PROTOBUF_VERSION || 'v0',
    'rmtt',
);
