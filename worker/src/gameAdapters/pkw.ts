import { UnrecoverableError } from 'bullmq';
import {
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    JobData,
    logging,
    RoomStartParams,
    PkwUserData,
    TableData,
} from 'shared';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';
import { pkwConfig } from './pkwConfig';

class PkwAdapter implements GamePlatformAdapter {
    constructor(
        private readonly gameId: number = 2,
        private readonly gameMode: GameMode = GameMode.NORMAL,
        private readonly gameType: GameType = GameType.NLHE,
    ) {}

    async init(): Promise<void> {
        await PkwMain.init(this.gameId, this.gameMode, this.gameType);
    }

    async play(user: PkwUserData, tableId: number, params: JobData, handler: JobDataHandler): Promise<void> {
        const botParams: RoomStartParams = {
            buyInMultiplier: params.buyInMultiplier ?? 100,
            rebuyEnabled: params.rebuyEnabled ?? true,
            rebuyThreshold: params.rebuyThreshold ?? 50,
            maxRebuyCount: params.maxRebuyCount ?? 100,
            withdrawAmount: params.withdrawAmount ?? 0,
            withdrawThreshold: params.withdrawThreshold ?? 0,
            profileName: params.profileName,
        };
        return PkwMain.pkwPlay(user, tableId, botParams, handler);
    }

    scan(user: PkwUserData, params: JobData, handler: JobDataHandler): Promise<void> {
        const { gameMode, currencies } = pkwConfig[params.appId];
        const tablesDataHandler = (tables: TableData[]) => {
            tables = tables.filter((table) => {
                return (
                    table.gameId === this.gameId &&
                    table.gameMode === gameMode &&
                    currencies.includes(table.currency)
                );
            });
            handler({ tables: tables.map((t) => ({ ...t, appId: params.appId })) });
        };
        return PkwMain.pkwScan(user, tablesDataHandler);
    }

    check(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: check`);
    }

    balance(): Promise<any> {
        throw new UnrecoverableError(`Unsupported game platform action: balance`);
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Adapter] stop', jobType);
        if (jobType == JobType.PLAY) {
            await PkwMain.finishGame();
        }
    }

    isMtt(): boolean {
        return false;
    }
}

export const cashAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.NLHE);
export const diamondAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.NLHE);
export const splashAdapter = new PkwAdapter(60, GameMode.SPLASH);
export const splashDiamondAdapter = new PkwAdapter(60, GameMode.SPLASH);
export const zoomAdapter = new PkwAdapter(40, GameMode.ZOOM);
export const shortDeckAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.SHORTDECK);
