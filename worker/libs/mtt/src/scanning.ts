import { AppType, <PERSON>DataHandler, logging } from 'shared';
import { HttpApis } from './httpApis';

const scanInterval = 10000;
const fetchDetailInterval = 3000;

export async function scanAndFetch(
    token: string,
    updateData: JobDataHandler,
    storeTournamentDetails: (id: number, data: any) => void,
    mttApiUrl: string,
): Promise<void> {
    return new Promise(() => {
        const httpApis = new HttpApis(mttApiUrl);
        let tournaments: any[] = [];
        const throttleDelay = createThrottler();

        const listTournaments = async () => {
            await throttleDelay();
            const data = await httpApis.requestMttTournamentList(token, 0);
            tournaments = data.TournamentInfos.map(item => toTournamentData(item, AppType.MTT));
            updateData({ tournaments });
        };

        let tournamentIdsToScan: number[] = [];
        const fetchTournamentDetails = async () => {
            /**
             * Fetches MTT tournaments details one by one.
             */
            if (tournamentIdsToScan.length === 0) {
                tournamentIdsToScan = tournaments
                    .filter((t) => {
                        return (
                            t.gameMode === 2 &&
                            ((t.status === 0 && t.startingTime <= Date.now() + 3600_000) ||
                                t.status === 1 ||
                                t.status === 2)
                        );
                    })
                    .filter((t) => t.id != null)
                    .map((t) => t.id);
            }

            const isInList = (id) => tournaments.some((t) => t.id === id);
            let nextIndexToFetch = tournamentIdsToScan.findIndex(isInList);
            if (nextIndexToFetch === -1) {
                tournamentIdsToScan = [];
                return;
            }
            const mttId = tournamentIdsToScan[nextIndexToFetch];
            tournamentIdsToScan.splice(0, nextIndexToFetch + 1);

            await throttleDelay();
            try {
                const data: any = await httpApis.requestMttTournamentDetail(token, mttId);
                if (!data?.TournamentDetail || data.ErrorCode) {
                    logging.warn('requestMttTournamentDetail error', { mttId, data });
                } else {
                    storeTournamentDetails(mttId, data);
                }
            } catch (error) {
                logging.error(`Failed to fetch tournament detail: ${mttId}`, error);
            }

        };

        listTournaments();
        setInterval(listTournaments, scanInterval);
        setInterval(fetchTournamentDetails, fetchDetailInterval);
    });
}

function createThrottler(delayMs: number = 1000) {
    let lastCall = 0;
    let queue = Promise.resolve();

    return function waitForThrottle(): Promise<void> {
        queue = queue.then(async () => {
            const now = Date.now();
            const next = Math.max(now, lastCall + delayMs);
            const wait = next - now;
            lastCall = next;
            if (wait > 0) {
                await new Promise((resolve) => setTimeout(resolve, wait));
            }
        });
        return queue;
    };
}

function toTournamentData(tournament: any, appId): {[key: string]: string | number | Date | boolean} {
    const joinedCount = tournament.JoinedCount || 0;
    const gamePool = tournament.GamePool || 0;
    const regFee = tournament.Detail.RegFee || 0;
    const overlay = gamePool - regFee * joinedCount;
    let lateRegistrationTime;
    if (tournament.TimeLeftSec) {
        lateRegistrationTime = new Date(Date.now() + tournament.TimeLeftSec * 1000);
    }

    let startingTime: Date;
    if (tournament.Detail.StartingTime instanceof Date) {
        startingTime = tournament.Detail.StartingTime;
    } else if (tournament.Detail.StartingTime) {
        startingTime = new Date(
            tournament.Detail.StartingTime.seconds * 1000 + tournament.Detail.StartingTime.nanos / 1000000,
        );
    }

    return {
        id: tournament.Detail.Id,
        appId: appId,
        tournamentName: tournament.Detail.TournamentName,
        tournamentNameEng: tournament.Detail.TournamentNameI18N,
        startingTime: startingTime,
        lateRegistrationTime: lateRegistrationTime,
        seats: tournament.Detail.Seats || 0,
        regFee: regFee,
        srvFee: tournament.Detail.SrvFee || 0,
        gamePool: gamePool,
        overlay: overlay,
        displayCurrency: tournament.Detail.DisplayCurrency,
        registeredCount: tournament.RegisteredCount || 0,
        joinedCount: joinedCount,
        status: tournament.Detail.Status || 0,
        mttMode: tournament.Detail.MttMode || 0,
        tournamentMode: tournament.Detail.TournamentMode || 0,
        gameMode: tournament.Detail.GameMode || 0,
        isSatelliteMode: tournament.Detail.IsSatelliteMode || false,
        signUpOptions: tournament.Detail.SignUpOptions || '',
        multiFlightId: tournament.Detail.MultiFlightId || 0,
        multiFlightLevel: tournament.Detail.MultiFlightLevel || 0,
    };
}
