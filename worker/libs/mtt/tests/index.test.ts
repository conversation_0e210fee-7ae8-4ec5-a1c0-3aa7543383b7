import { mock, test } from 'node:test';
import assert from 'node:assert';
import { MttMain } from '../src/index';
import { HttpApis } from '../src/httpApis';
import { MttConfigType } from 'shared';
import Long from 'long';

const mockConfig: MttConfigType = {
    mttWorld: 'ws://test-world.com',
    mttGame: 'ws://test-game.com',
    mttApi: 'https://test-api.com',
};

test('MttMain - balance method - successful response with usable tickets', async () => {
    MttMain.init(
        () => {}, // storeTournamentDetails
        () => Promise.resolve({}), // fetchTournamentDetails
        'v2', // protoVersion
        mockConfig,
    );

    const mockTicketsData = {
        ToolInBackpacks: [
            { ToolId: 1, Usable: true, Id: Long.fromNumber(101) },
            { ToolId: 2, Usable: false, Id: 102 },
            { ToolId: 3, Usable: true, Expiry: new Date(Date.now() - 1000_000), Id: 103 },
            { ToolId: 4, Usable: true, Id: 104 },
            { ToolId: 5, Usable: false, Id: 105 },
        ],
    };

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.balance('test-token');

    assert.deepStrictEqual(result, [
        { toolId: 1, ticketId: 101 },
        { toolId: 4, ticketId: 104 },
    ]);
    assert.strictEqual(mockRequestPlayerTicketsData.mock.calls.length, 1);
});

test('MttMain - balance method - empty tickets response', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        'v2',
        mockConfig,
    );

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve([]));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.balance('test-token');

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - null response from API', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        'v2',
        mockConfig,
    );

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(null));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.balance('test-token');

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - all tickets unusable', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        'v2',
        mockConfig,
    );

    const mockTicketsData = {
        ToolInBackpacks: [
            { ToolId: 1, Usable: false, Id: Long.fromNumber(201) },
            { ToolId: 2, Usable: false, Id: 202 },
            { ToolId: 3, Usable: false, Id: 203 },
        ],
    };

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.balance('test-token');

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - usable ticket', async () => {
    const mttInstance = new MttMain();

    (mttInstance as any).urlConfig = {
        mttWorld: 'ws://test-world.com',
        mttGame: 'ws://test-game.com',
        mttApi: 'https://test-api.com',
    };

    const mockTicketsData = {
        ToolInBackpacks: [
            {
                ToolId: 42,
                Usable: true,
                Id: Long.fromNumber(142),
            },
        ],
    };

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.balance('test-token');

    assert.deepStrictEqual(result, [{ toolId: 42, ticketId: 142 }]);
});
