import { test, mock } from 'node:test';
import assert from 'node:assert/strict';

import { signupAndPlay } from '../src/mtt'
import { DelayService, logging, sleep } from 'shared';
import { GamePlayer, WorldPlayer } from '../src/player';
import { CustomWebsocket } from '../src/customWebsocket';
import { commonProto } from '../src/mtt/pb/v2/commonProto'
import { holdem } from '../src/mtt/pb/v2/holdem'
import { FakeWebSocket } from './player.test';
import { HttpApis } from '../src/httpApis';
import { CONNECTION_STATUS } from '../src/enums';
import { url } from 'node:inspector';

const PROXY = undefined;

Object.assign(logging, console);

const progressHandler = mock.fn();
const fetchTournamentDetails = mock.fn((mttId) => null);
const urlConfig = {
	mttWorld: 'world',
	mttGame: 'game',
	mttApi: 'api',
};

const tournamentDetails = {
	TournamentDetail: {
		// TODO: Maybe test payout structure parsing too
		PlayersDetail: [
			{ UserId: 69420, Coins: 100500, Rank: 13 } // some other player
		]
	}
};

test('mtt e2e', async () => {
	const playerId = 13;
	const mttId = 42;
	DelayService.prototype.calculateDelayMs = () => 0;
	let worldWs = new FakeWebSocket('ws://fake-url');
	let gameWs = new FakeWebSocket('ws://fake-url');
	let worldPlayer: WorldPlayer = {} as any;
	let gamePlayer: GamePlayer = {} as any;

	mock.method(WorldPlayer.prototype, 'createWebsocket', (function() {
		// console.log('Mocked createWebsocket called');
		this.websocket = worldWs;
		worldPlayer = this;
	}));

	mock.method(GamePlayer.prototype, 'createWebsocket', (function() {
		// console.log('Mocked createWebsocket called');
		this.websocket = gameWs;
		gamePlayer = this;
	}));

	const gamePromise = signupAndPlay(
		'token', mttId, 0,
		progressHandler,
		fetchTournamentDetails,
		urlConfig,
		PROXY,
		'myProfile'
	);

	// world player login
	// fetch mtt data
	// signup to tournament
	HttpApis.prototype.requestMttTournamentDetail = mock.fn(async function(token, mttId) {
		return tournamentDetails;
	});
	HttpApis.prototype.requestJoinedTournaments = () => ({
		MttList: [] // no joined tournaments
	}) as any;
	HttpApis.prototype.requestMttPlayerSignUp = () => ({ // successful signup
		ErrorCode: 0,
		PlayerId: 'test-player-id',
		ExternalErrorCode: 1,
		tournamentId: 'tournament-1',
	});
	HttpApis.prototype.requestMttMultiTable = () => ({
		UserGameInfo: [
			{ SngMttLevelId: mttId, MttTournamentStatus: 1 } // tournament ready
		]
	}) as any;
	await worldWs.callHandler(commonProto.SocketMessageId.User_Login_Response, {
		ErrorCode: 0,
		UserData: {
			Id: playerId,
		},
		Token: 'myToken',
		JoinedTournaments: [],
	});
	assert.equal(gamePlayer.dataManager.token, 'myToken');

	// await tournament details
	assert.equal(fetchTournamentDetails.mock.callCount(), 1);
	assert.equal(fetchTournamentDetails.mock.calls[0].arguments[0], 42);
	assert.deepEqual(HttpApis.prototype.requestMttTournamentDetail.mock.calls[0].arguments, ['myToken', 42]);

	// game player logging in
	await sleep(10);
	assert.equal(gamePlayer.connectionStatus, CONNECTION_STATUS.CONNECTED);

	await gameWs.callHandler(holdem.MessageId.UserTokenRes, {});
	// await sleep(10);
	assert.equal(worldPlayer.connectionStatus, CONNECTION_STATUS.STOP);
	// assert.equal(gamePlayer.connectionStatus, CONNECTION_STATUS.LOGGED_IN);

	// finish
	await gameWs.callHandler(gamePlayer.ids.RewardMsg, {
		mttId: mttId,
		userId: playerId,
	});
});

test('mtt, player can connect if already signed up', async () => {
	const playerId = 13;
	const mttId = 42;
	DelayService.prototype.calculateDelayMs = () => 0;
	let worldWs = new FakeWebSocket('ws://fake-url');
	let gameWs = new FakeWebSocket('ws://fake-url');
	let worldPlayer: WorldPlayer = {} as any;
	let gamePlayer: GamePlayer = {} as any;

	mock.method(WorldPlayer.prototype, 'createWebsocket', (function() {
		this.websocket = worldWs;
		worldPlayer = this;
	}));

	mock.method(GamePlayer.prototype, 'createWebsocket', (function() {
		this.websocket = gameWs;
		gamePlayer = this;
	}));

	const gamePromise = signupAndPlay(
		'token', mttId, 0,
		progressHandler,
		fetchTournamentDetails,
		urlConfig,
		PROXY,
		'myProfile'
	);

	// world player login
	// fetch mtt data
	// signup to tournament
	HttpApis.prototype.requestMttTournamentDetail = mock.fn(async (token, mttId) => tournamentDetails);
	HttpApis.prototype.requestJoinedTournaments = () => ({
		MttList: [
			{ TournamentId: mttId, JoinStatus: 1, Status: 2 } // already signed up, tournament in progress
		]
	}) as any;
	HttpApis.prototype.requestMttPlayerSignUp = mock.fn();
	HttpApis.prototype.requestMttMultiTable = async () => ({
		UserGameInfo: [
			{ SngMttLevelId: mttId, MttTournamentStatus: 1 } // tournament ready
		]
	});
	await worldWs.callHandler(commonProto.SocketMessageId.User_Login_Response, {
		ErrorCode: 0,
		UserData: {
			Id: playerId,
		},
		Token: 'myToken',
		JoinedTournaments: [
			{ TournamentId: 'tournament-1', JoinStatus: 1 } // already signed up
		],
	});

	// should not call signup if already signed up
	assert.deepEqual(HttpApis.prototype.requestMttTournamentDetail.mock.calls[0].arguments, ['myToken', 42]);
	assert.equal(HttpApis.prototype.requestMttPlayerSignUp.mock.callCount(), 0);

	// finish
	await gameWs.callHandler(gamePlayer.ids.RewardMsg, {
		mttId: mttId,
		userId: playerId,
	});
});
