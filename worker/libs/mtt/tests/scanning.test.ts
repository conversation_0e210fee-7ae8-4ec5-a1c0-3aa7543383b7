import { test, mock } from 'node:test';
import assert from 'node:assert';
import { HttpApis } from '../src/httpApis';
import { scanAndFetch } from '../src/scanning';

function fixedNow() {
  return 1_700_000_000_000; // fixed epoch ms for deterministic tests
}

function makeTournamentInfo(id: number, gameMode: number, status: number, startOffsetSec: number) {
  return {
    Detail: {
      Id: id,
      Status: status,
      GameMode: gameMode,
      RegFee: 1,
      StartingTime: {
        seconds: Math.floor((fixedNow() + startOffsetSec * 1000) / 1000),
        nanos: 0,
      },
    },
    JoinedCount: 0,
    GamePool: 0,
  };
}

// Utility to fake timers to avoid real intervals and throttling waits
function useFakeTimers() {
  const originalSetInterval = global.setInterval;
  const originalSetTimeout = global.setTimeout;
  const intervals: Array<() => any> = [];

  // capture intervals, don't schedule
  // @ts-ignore
  global.setInterval = (fn: <PERSON>r<PERSON><PERSON><PERSON>, _ms?: number, ..._args: any[]) => {
    intervals.push(fn as any);
    return intervals.length; // fake id
  };
  // resolve timeouts immediately
  // @ts-ignore
  global.setTimeout = (fn: TimerHandler, _ms?: number, ..._args: any[]) => {
    (fn as any)();
    return 0 as any;
  };

  return {
    restore() {
      global.setInterval = originalSetInterval;
      global.setTimeout = originalSetTimeout;
    },
    runInterval(index: number, times: number = 1) {
      for (let i = 0; i < times; i++) {
        intervals[index]?.();
      }
    },
  };
}

async function waitFor(cond: () => boolean, attempts = 100) {
  for (let i = 0; i < attempts; i++) {
    if (cond()) return;
    await Promise.resolve();
  }
  throw new Error('waitFor timeout');
}

// 1) lists tournaments and fetches eligible details
test('scanAndFetch lists and stores eligible tournament details', async () => {
  const timers = useFakeTimers();
  const originalNow = Date.now;
  Date.now = fixedNow;

  const calls: any[] = [];
  const updateData = (data: any) => calls.push(data);
  const stored: Array<[number, any]> = [];
  const storeTournamentDetails = (id: number, data: any) => stored.push([id, data]);

  // Prepare list: only gameMode===2 and (status 0 & <=1h) or status 1/2 are eligible
  const eligibleSoon = makeTournamentInfo(101, 2, 0, 1800); // starts in 30m => eligible
  const eligibleRunning = makeTournamentInfo(102, 2, 1, 7200); // status 1 => eligible
  const ineligibleLate = makeTournamentInfo(103, 2, 0, 3700); // >1h => not eligible
  const wrongMode = makeTournamentInfo(104, 1, 1, 0); // wrong gameMode

  const apiProto: any = HttpApis.prototype;
  const listSpy = mock.method(apiProto, 'requestMttTournamentList', async () => ({
    TournamentInfos: [eligibleSoon, eligibleRunning, ineligibleLate, wrongMode],
  }));
  const detailSpy = mock.method(apiProto, 'requestMttTournamentDetail', async (_token: string, mttId: number) => ({
    TournamentDetail: { id: mttId },
  }));

  try {
    // Start scanning (do not await, it never resolves)
    // @ts-ignore
    scanAndFetch('token', updateData, storeTournamentDetails, 'https://api');

    // Trigger the list interval once to ensure data is loaded
    timers.runInterval(0, 1);

    // Wait until listSpy has been called at least once
    await waitFor(() => listSpy.mock.calls.length >= 1);

    // Now trigger fetchTournamentDetails interval twice to drain two eligible IDs
    timers.runInterval(1, 2);

    // Wait until both details have been stored
    await waitFor(() => stored.length === 2);

    // Assert update was sent with tournaments
    assert.ok(calls.length >= 1);
    assert.strictEqual(calls[0].tournaments.length, 4);

    // Only eligibleSoon and eligibleRunning should be fetched/stored
    const storedIds = stored.map(([id]) => id).sort();
    assert.deepStrictEqual(storedIds, [101, 102]);

    // Verify HttpApis methods were invoked
    assert.ok(listSpy.mock.calls.length >= 1);
    assert.strictEqual(detailSpy.mock.calls.length, 2);
  } finally {
    // cleanup
    timers.restore();
    Date.now = originalNow;
    mock.restoreAll();
  }
});

// 2) skips storing when detail missing or has ErrorCode
test('scanAndFetch skips store when detail missing or ErrorCode present', async () => {
  const timers = useFakeTimers();
  const originalNow = Date.now;
  Date.now = fixedNow;

  const updateData = (_: any) => {};
  const stored: number[] = [];
  const storeTournamentDetails = (id: number, _data: any) => stored.push(id);

  const t1 = makeTournamentInfo(201, 2, 1, 0);
  const t2 = makeTournamentInfo(202, 2, 1, 0);

  const apiProto: any = HttpApis.prototype;
  const listSpy = mock.method(apiProto, 'requestMttTournamentList', async () => ({
    TournamentInfos: [t1, t2],
  }));

  let callCount = 0;
  const detailSpy = mock.method(apiProto, 'requestMttTournamentDetail', async (_token: string, _mttId: number) => {
    callCount++;
    if (callCount === 1) return { ErrorCode: 10910 } as any;
    if (callCount === 2) return {} as any; // missing TournamentDetail
    return {} as any;
  });

  try {
    // @ts-ignore
    scanAndFetch('token', updateData, storeTournamentDetails, 'https://api');

    // Trigger list once and wait until it has been called
    timers.runInterval(0, 1);
    await waitFor(() => listSpy.mock.calls.length >= 1);

    // Trigger two detail fetch attempts
    timers.runInterval(1, 2);

    // Ensure no stores due to invalid detail responses
    await waitFor(() => detailSpy.mock.calls.length >= 2);
    assert.deepStrictEqual(stored, []);

    // Verify list was called at least once
    assert.ok(listSpy.mock.calls.length >= 1);
  } finally {
    timers.restore();
    Date.now = originalNow;
    mock.restoreAll();
  }
});
