import { featureFlagValue, GAME_PHASE, logging, StrategyResponseAction, StrategyAction } from 'shared';

function randomBetween(min: number, max: number): number {
    return Math.random() * (max - min) + min;
}

export const ExtraTimeFeature = {
    getFeatureParameters: async ({
        gamePhase,
        strategyActions,
        greatestBet,
        selfInfoRoundBet,
        blindValue,
        currentPot,
    }: {
        gamePhase: GAME_PHASE;
        strategyActions: StrategyAction[];
        greatestBet: number;
        selfInfoRoundBet: number;
        blindValue: number;
        currentPot: number;
    }): Promise<{
        isApplicable: boolean;
        waitBeforeRequestMs?: number;
        waitAfterRequestMs?: number;
    }> => {
        const isApplicable = await ExtraTimeFeature._isApplicable({
            gamePhase,
            strategyActions,
            greatestBet,
            selfInfoRoundBet,
            blindValue,
            currentPot,
        });

        if (isApplicable) {
            const { waitBeforeRequestMs, waitAfterRequestMs } = ExtraTimeFeature._getWaitingData();
            return {
                isApplicable,
                waitBeforeRequestMs,
                waitAfterRequestMs,
            };
        }
        return {
            isApplicable,
        };
    },

    _isApplicable: async ({
        gamePhase,
        strategyActions,
        greatestBet,
        selfInfoRoundBet,
        blindValue,
        currentPot,
    }: {
        gamePhase: GAME_PHASE;
        strategyActions: StrategyAction[];
        greatestBet: number;
        selfInfoRoundBet: number;
        blindValue: number;
        currentPot: number;
    }): Promise<boolean> => {
        const enabled = await ExtraTimeFeature._extraTimeEnabled(gamePhase);

        if (!enabled) {
            return false;
        }

        let callProbability = 0;
        let foldProbability = 0;
        strategyActions.forEach((item) => {
            if (item.action.action === StrategyResponseAction.CALL) {
                callProbability = item.hand_strategy;
            } else if (item.action.action === StrategyResponseAction.FOLD) {
                foldProbability = item.hand_strategy;
            }
        });

        const callFoldSumMinimumProbability = await ExtraTimeFeature._callFoldSumMinimumProbability();
        const foldMinusCallMaximumProbability = await ExtraTimeFeature._foldMinusCallMaximumProbability();

        if (
            callProbability + foldProbability < callFoldSumMinimumProbability ||
            Math.abs(foldProbability - callProbability) > foldMinusCallMaximumProbability
        ) {
            logging.info(
                '[ExtraTimeFeature] - call / fold probabilities do not match thresholds, cancelled',
                {
                    callProbability,
                    foldProbability,
                    callFoldSumMinimumProbability,
                    foldMinusCallMaximumProbability,
                },
            );
            return false;
        }

        const amountToCallMinimumBB = await ExtraTimeFeature._amountToCallMinimumBB();
        const selfRoundBetMinimumBB = await ExtraTimeFeature._selfRoundBetMinimumBB();
        const potMinimumBB = await ExtraTimeFeature._potMinimumBB();
        const amountToCall = greatestBet - selfInfoRoundBet;
        if (
            amountToCall > amountToCallMinimumBB * blindValue &&
            selfInfoRoundBet > selfRoundBetMinimumBB * blindValue
        ) {
            logging.info(
                `[ExtraTimeFeature] - amountToCall / selfInfoRoundBet matches thresholds: amountToCall ${amountToCall} > ${amountToCallMinimumBB * blindValue}) and selfInfoRoundBet ${selfInfoRoundBet} > ${selfRoundBetMinimumBB * blindValue}`,
                {
                    amountToCall,
                    selfInfoRoundBet,
                    greatestBet,
                    blindValue,
                    amountToCallMinimumBB,
                    selfRoundBetMinimumBB,
                },
            );
            return ExtraTimeFeature._meetsProbability();
        } else if (currentPot > potMinimumBB * blindValue) {
            logging.info(
                `[ExtraTimeFeature] - pot match threshold: currentPot ${currentPot} > ${potMinimumBB * blindValue}`,
                {
                    currentPot,
                    blindValue,
                    potMinimumBB,
                },
            );
            return ExtraTimeFeature._meetsProbability();
        }

        return false;
    },

    _getWaitingData: (): {
        waitBeforeRequestMs: number;
        waitAfterRequestMs: number;
    } => {
        let waitBeforeRequestMs = 0;
        // We expect to wait between  9 - 12 seconds before requesting extra time for R2 (hardcoded for now)
        const minWaitExtra = 9000;
        const maxWaitExtra = 12000;
        waitBeforeRequestMs = Math.round(randomBetween(minWaitExtra, maxWaitExtra));

        // We expect to wait between  3 - 9 seconds before making game action for R2 (hardcoded for now)
        const minWaitAction = 3000;
        const maxWaitAction = 9000;
        const waitAfterRequestMs = Math.round(randomBetween(minWaitAction, maxWaitAction));

        return {
            waitBeforeRequestMs,
            waitAfterRequestMs,
        };
    },

    _meetsProbability: async (): Promise<boolean> => {
        const probability = await ExtraTimeFeature._useProbability();

        if (Math.random() < probability) {
            return true;
        }

        logging.info('[ExtraTimeFeature] - chance is not met', {
            probability,
        });

        return false;
    },

    // These feature-flags getter functions are used for mocking in tests
    _extraTimeEnabled: async (gamePhase: GAME_PHASE): Promise<boolean> => {
        return Boolean(await featureFlagValue('extra-time-feature-enabled', { gamePhase }, false));
    },

    _callFoldSumMinimumProbability: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-call-fold-sum-minimum-probability', {}, 0.6);
    },

    _foldMinusCallMaximumProbability: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-fold-minus-call-maximum-probability', {}, 0.35);
    },

    _amountToCallMinimumBB: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-amount-to-call-minimum-bb', {}, 70);
    },

    _selfRoundBetMinimumBB: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-self-round-bet-minimum-bb', {}, 24);
    },

    _potMinimumBB: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-amount-pot-minimum-bb', {}, 179);
    },

    _useProbability: async (): Promise<number> => {
        return featureFlagValue('extra-time-feature-use-probability', {}, 0.5);
    },
};
