let queue: { [key: string]: Function[] } = {};

export const MessageCenter = {
    register(msg: string, callback: Function) {
        if (!queue[msg]) {
            queue[msg] = [];
        }
        queue[msg].push(callback);
    },

    send(msg: string, params: any = null): void {
        if (!queue[msg]) {
            return;
        }
        queue[msg].forEach((callback) => callback(params));
    },

    clear() {
        queue = {};
    }
}
