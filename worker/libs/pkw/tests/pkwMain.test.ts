import { mock, test } from 'node:test';
import assert from 'node:assert';
import { PkwMain } from '../src/pkw_ts/PkwMain';
import pkwGame from '../src/pkw_ts/pkwGame';
import { GameMode } from 'shared';
import { DelayService } from 'shared';
import cv from '../src/pkw_ts/cv';
import { register } from 'node:module';


test('PkwMain sets gameModeCode to NORMAL for any GameMode', async () => {
	for await (const mode of Object.values(GameMode)) {
		console.log(`Testing gameMode: ${mode}`);
		if (mode === GameMode.BOMB) {
			assert.rejects(async () => {
				await PkwMain.init(undefined, mode);
			});
			continue;
		}

		mock.method(cv, 'initCV', mock.fn());
		cv.MessageCenter = { register: () => { } } as any; // Mock MessageCenter to avoid errors
		const delayInitMock = mock.method(DelayService.prototype, 'initPromise');

		await PkwMain.init(undefined, mode);
		assert.equal(pkwGame['_gameModeCode'], GameMode.NORMAL, `Expected gameModeCode to be NORMAL for ${mode}`);
		assert.equal(delayInitMock.mock.calls[0].arguments[0], mode, `Expected DelayService to be initialized with ${mode}`);
	}
});
