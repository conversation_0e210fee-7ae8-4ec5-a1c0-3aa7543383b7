import { mock, test } from 'node:test';
import assert from 'node:assert';
import pkwGame from '../src/pkw_ts/pkwGame';
import { protocol as game_pb } from '../src/proto/gs_protocol';
import cv from '../src/pkw_ts/cv';
import { GameNetWork } from '../src/pkw_ts/network/GameNetWork';
import { NetWork } from '../src/pkw_ts/network/NetWork';
import { NetworkMock } from './gameplay.test';
import { GameState, sleep, StrategyResponseAction } from 'shared';
import pkwRoom from '../src/pkw_ts/pkwRoom';
import { ActionType  } from '../src/pkw_ts/tools/Enum';
import { ExtraTimeFeature } from '../src/pkw_ts/features/extraTime';

test('pkwGame calculates greatestBet', async () => {
	pkwGame['_greatestBet'] = 199;
	await pkwGame['OnActionTurn']({
		players: [
			{playerid: 1, name: 'p1', round_bet: 100},
			// {playerid: 2, name: 'p2', round_bet: 200},
		]
	} as game_pb.NoticePlayerActionTurn);

	assert.equal(pkwGame['_greatestBet'], 100);

	await pkwGame['OnActionTurn']({
		players: [
			{playerid: 1, name: 'p1', round_bet: 100},
			{playerid: 2, name: 'p2', round_bet: 200},
		]
	} as game_pb.NoticePlayerActionTurn);

	assert.equal(pkwGame['_greatestBet'], 200);
});

test('pkwGame handles NoticePlayerActionTurn: sends AllIn if strategyservice returns bet and not sufficient funds', async () => {
	cv.GameDataManager = {
		tRoomData: {
			u32RoomId: 42
		}
	} as any;
	cv.netWork = NetworkMock as any;
	cv.gameNet = GameNetWork.getInstance();
	cv.MessageCenter.clear();
	cv.gameNet.init();
	cv.gameNet.decodePB = (_, pbuf) => pbuf;
	pkwRoom.setUserStatus = () => {};

	pkwGame.setDelayService({
		calculateDelayMs: () => 0,
	} as any);
	pkwGame.init();

	pkwGame['_selfUid'] = 13;
	pkwGame['_selfInfo'] = {
		stake: 200,
		round_bet: 69,
	};
	pkwGame['_gameSt'] = {
		roomid: 42,
	} as any;
	pkwGame['_roomSt'] = {};
	pkwGame['getCurrentPot'] = () => 0;
	pkwGame.OnResetGameNoti({
		players: [
			{playerid: 99, seatid: 1, stake: 100500, in_game: true },
			{playerid: 13, seatid: 2, stake: 200, in_game: true },
		]
	} as any);
	assert.equal(pkwGame['_currentStack'], 200);

	ExtraTimeFeature.getFeatureParameters = async () => ({ isApplicable: false });
	GameState.prototype.setPlayerCards = mock.fn();
	GameState.prototype.fetchStrategy = async () => ({
		action: StrategyResponseAction.BET,
		amount: 100500,
	});

	cv.gameNet.RequestAction = mock.fn();

	await cv.gameNet.NoticePlayerActionTurn({
		roomid: 42,
		curr_action_uid: 13,
		holdcards: [{ number: 11, suit: 1 }, { number: 12, suit: 2 }],
		players: [],
		ActionSeq: 'xxx_yyy'
	});

	await sleep(10);

	assert.deepEqual(GameState.prototype.setPlayerCards.mock.calls[0].arguments, ['13', 'KcAh']);

	assert.equal(cv.gameNet.RequestAction.mock.calls.length, 1);
	assert.deepEqual(cv.gameNet.RequestAction.mock.calls[0].arguments, [42, ActionType.Enum_Action_Allin, 269, undefined, undefined, 'xxx_yyy']); // AllIn
});
