import { test } from 'node:test';
import assert from 'node:assert';

import { ExtraTimeFeature } from '../src/pkw_ts/features/extraTime';
import { StrategyResponseAction, GAME_PHASE } from 'shared';

test.beforeEach(() => {
    ExtraTimeFeature._useProbability = async () => 1;
    ExtraTimeFeature._extraTimeEnabled = async () => true;
    ExtraTimeFeature._callFoldSumMinimumProbability = async () => 0.6;
    ExtraTimeFeature._foldMinusCallMaximumProbability = async () => 0.35;
});

test('ExtraTimeFeature._isApplicable - disabled => false', async () => {
    ExtraTimeFeature._extraTimeEnabled = async () => false;

    const result = await ExtraTimeFeature._isApplicable({
        gamePhase: GAME_PHASE.PREFLOP,
        strategyActions: [],
        greatestBet: 0,
        selfInfoRoundBet: 0,
        blindValue: 100,
        currentPot: 0,
    });
    assert.equal(result, false);
});

test('ExtraTimeFeature._isApplicable - thresholds cancel by call/fold mismatch => false', async () => {
    const result = await ExtraTimeFeature._isApplicable({
        gamePhase: GAME_PHASE.FLOP,
        strategyActions: [
            { action: { action: StrategyResponseAction.CALL }, hand_strategy: 0.05, action_name: 'call' },
            { action: { action: StrategyResponseAction.FOLD }, hand_strategy: 0.5, action_name: 'fold' },
        ],
        greatestBet: 0,
        selfInfoRoundBet: 0,
        blindValue: 100,
        currentPot: 0,
    });
    assert.equal(result, false);
});

test('ExtraTimeFeature._isApplicable - amountToCall/selfRoundBet exceed thresholds => true (with prob=1)', async () => {
    ExtraTimeFeature._amountToCallMinimumBB = async () => 70;
    ExtraTimeFeature._selfRoundBetMinimumBB = async () => 24;

    const blindValue = 100; // cents
    const selfInfoRoundBet = 2500; // 25 BB > 24 BB threshold
    const greatestBet = selfInfoRoundBet + 8000; // amountToCall 80 BB > 70 BB threshold

    const result = await ExtraTimeFeature._isApplicable({
        gamePhase: GAME_PHASE.TURN,
        strategyActions: [
            { action: { action: StrategyResponseAction.CALL }, hand_strategy: 0.4, action_name: 'call' },
            { action: { action: StrategyResponseAction.FOLD }, hand_strategy: 0.4, action_name: 'fold' },
        ],
        greatestBet,
        selfInfoRoundBet,
        blindValue,
        currentPot: 0,
    });
    assert.equal(result, true);
});

test('ExtraTimeFeature._isApplicable - pot exceeds threshold => true', async () => {
    ExtraTimeFeature._potMinimumBB = async () => 179;

    const blindValue = 100;
    const currentPot = 179 * blindValue + 1; // just above threshold
    const result = await ExtraTimeFeature._isApplicable({
        gamePhase: GAME_PHASE.RIVER,
        strategyActions: [
            { action: { action: StrategyResponseAction.CALL }, hand_strategy: 0.5 },
            { action: { action: StrategyResponseAction.FOLD }, hand_strategy: 0.5 },
        ] as any,
        greatestBet: 0,
        selfInfoRoundBet: 0,
        blindValue,
        currentPot,
    });
    assert.equal(result, true);
});

test('ExtraTimeFeature._isApplicable - pot exceeds threshold but prob=0 => false', async () => {
    ExtraTimeFeature._useProbability = async () => 0;
    ExtraTimeFeature._potMinimumBB = async () => 100;

    const blindValue = 100;
    const currentPot = 100 * blindValue + 1000;
    const result = await ExtraTimeFeature._isApplicable({
        gamePhase: GAME_PHASE.RIVER,
        strategyActions: [
            { action: { action: StrategyResponseAction.CALL }, hand_strategy: 0.5, action_name: 'call' },
            { action: { action: StrategyResponseAction.FOLD }, hand_strategy: 0.5, action_name: 'fold' },
        ],
        greatestBet: 0,
        selfInfoRoundBet: 0,
        blindValue,
        currentPot,
    });
    assert.equal(result, false);
});

// Tests for _getWaitingData function
test('ExtraTimeFeature._getWaitingData - normal stopwatch value', async () => {
    const result = await ExtraTimeFeature._getWaitingData();

    // waitBeforeRequestMs should be between 7000-10000 (9000-2000 to 12000-2000)
    assert.ok(result.waitBeforeRequestMs >= 9000);
    assert.ok(result.waitBeforeRequestMs <= 12000);

    // waitAfterRequestMs should be between 3000-9000
    assert.ok(result.waitAfterRequestMs >= 3000);
    assert.ok(result.waitAfterRequestMs <= 9000);

    // Both should be integers
    assert.equal(Number.isInteger(result.waitBeforeRequestMs), true);
    assert.equal(Number.isInteger(result.waitAfterRequestMs), true);
});
