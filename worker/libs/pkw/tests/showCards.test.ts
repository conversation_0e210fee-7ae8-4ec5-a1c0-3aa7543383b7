import { test } from 'node:test';
import assert from 'node:assert';
import { OpponentHand, ShowCardsFeature } from '../src/pkw_ts/features/showCards';

test.beforeEach(() => {
    ShowCardsFeature._isEnabled = () => Promise.resolve(true);
    ShowCardsFeature._showCardsProbability = () => Promise.resolve(1);
});

test('showCardsIndexes - feature flag disabled => empty', async () => {
    ShowCardsFeature._isEnabled = () => Promise.resolve(false);

    const opponentHands: OpponentHand[] = [{ cardSymbols: ['2d', '2c'] }, { cardSymbols: ['3h', '3s'] }];
    const communityCards = ['As', 'Kh', 'Qd', 'Tc', '9s'];
    const holeCards = ['Ac', 'Kd'];

    const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
    assert.deepEqual(result, []);
});

test('showCardsIndexes - Two pairs: do not show cards as it is not a rare combination', async () => {
    const opponentHands: OpponentHand[] = [{ cardSymbols: ['2d', '2c'] }, { cardSymbols: ['3h', '3s'] }];
    const communityCards = ['As', 'Kh', 'Qd', 'Tc', '9s'];
    const holeCards = ['Ac', 'Kd'];

    const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
    assert.deepEqual(result, []);
});

test('showCardsIndexes - Three of a kind -> [0]', async () => {
    const opponentHands: OpponentHand[] = [{ cardSymbols: ['3c', '4d'] }, { cardSymbols: ['5h', '6s'] }];
    const communityCards = ['2s', '7h', '9d', 'Ac', 'Ad'];
    const holeCards = ['As', 'Kd'];

    const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
    assert.deepEqual(result, [0]);
});

test('showCardsIndexes - Three of a kind -> [1]', async () => {
    const opponentHands: OpponentHand[] = [{ cardSymbols: ['3c', '4d'] }, { cardSymbols: ['5h', '6s'] }];
    const communityCards = ['2s', '7h', '9d', 'Kc', 'Kd'];
    const holeCards = ['As', 'Kh'];

    const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
    assert.deepEqual(result, [1]);
});

test('showCardsIndexes - Straight -> [0,1]', async () => {
    const opponentHands: OpponentHand[] = [{ cardSymbols: ['3c', '4d'] }, { cardSymbols: ['5h', '6s'] }];
    const communityCards = ['As', 'Kh', 'Qd', '3h', '4h'];
    const holeCards = ['Js', 'Th'];

    const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
    assert.deepEqual(result, [0, 1]);
});

test('showCardsIndexes - Three of a kind - reduced probability', async () => {
    ShowCardsFeature._showCardsProbability = () => Promise.resolve(0.1);

    const opponentHands: OpponentHand[] = [{ cardSymbols: ['3c', '4d'] }, { cardSymbols: ['5h', '6s'] }];
    const communityCards = ['2s', '7h', '9d', 'Kc', 'Kd'];
    const holeCards = ['As', 'Kh'];

    let matchCount = 0;
    const iterations = 500;
    for (let i = 0; i < iterations; i++) {
        const result = await ShowCardsFeature.showCardsIndexes(opponentHands, communityCards, holeCards);
        if (Array.isArray(result) && result.length === 1 && result[0] === 1) {
            matchCount++;
        }
    }
    const percentage = (matchCount / iterations) * 100;
    assert.ok(percentage > 7 && percentage < 15, `expected about 10% matches, got ${percentage} %`);
});
