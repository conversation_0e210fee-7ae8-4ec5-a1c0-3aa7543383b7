import * as GameLogic from "./game_logic.js";

import { ProtoUtil } from './proto_utils';

import { CustomWebsocketResponse, GameError, MESSAGE_TYPES, POKER_ACTIONS, User } from './types'
import querystring from 'querystring';

import WebSocket from 'ws';
import { decryptAESBytes } from './encrypt_utils.js';
import { BattleRoomMsg } from "protobuf/MsgDeliverRespProto.js";
import { createLogger } from "./logging";

const console = createLogger("FriendsWS");

let client: WebSocket | null = null;
let lastCallbackCacheClean = Date.now();

type CustomWebsocketCallback = (resp: CustomWebsocketResponse) => void;
const callbackMap: Record<string, { callback: CustomWebsocketCallback; created_time: number }> = {};

let currentUser = undefined as User;
let currentRoomId = undefined as number;

let resolveGameEnd: (value: void) => void;

export const onExitedRoomPromise = new Promise<void>((resolve) => {
    resolveGameEnd = resolve;
});

export async function buyIn(amount: number): Promise<BattleRoomMsg> {
    const resp = await sendAsyncWebsocketMessage("WP_addScore", { score: amount });
    return resp.msgBody as BattleRoomMsg;
};

export async function sendRequestSeat(seatNum: number): Promise<BattleRoomMsg> {
    const response = await sendAsyncWebsocketMessage("C_sit", { seatNum });
    if (response.errorCode) {
        throw new GameError(`Error requesting seat: ${response.errMsg}`, response.errorCode);
    }
    return response.msgBody as BattleRoomMsg;
};

export async function sendJoinRoom(): Promise<BattleRoomMsg> {
    const resp = await sendAsyncWebsocketMessage("C_joinRoom", null);
    console.log("Joined room successfully");
    if (resp.msgType === 'C_closeRoomNotify') {
        throw new GameError("Room closed", resp.errorCode);
    }
    return resp.msgBody as BattleRoomMsg;
};

export async function returnToSeat() {
    await sendAsyncWebsocketMessage("WP_backSeat", null);
};

export async function sendStartGame() {
    const resp = await sendAsyncWebsocketMessage("C_startGame", null);

    if (resp.errorCode) {
        throw new GameError(`Error starting game: ${resp.errMsg}`, resp.errorCode);
    } else {
        console.log("Game started successfully");
    }
}

export async function exitRoom() {
    if (client) {
        await sendAsyncWebsocketMessage("C_exitRoom", null);
        client.close();
        console.log("Exited room and closed WebSocket connection");
    }
    resolveGameEnd();
}

const ACTION_TO_MESSAGE = {
    [POKER_ACTIONS.CHECK]: "WP_check",
    [POKER_ACTIONS.CALL]: "WP_call",
    [POKER_ACTIONS.RAISE]: "WP_raise",
    [POKER_ACTIONS.FOLD]: "WP_fold",
    [POKER_ACTIONS.ALL_IN]: "WP_allin"
}
export async function sendHandAction(action: POKER_ACTIONS, amount: number) {
    const data = action === POKER_ACTIONS.RAISE ? { raiseScore: amount } : null;
    const resp = await sendAsyncWebsocketMessage(ACTION_TO_MESSAGE[action], data)

    if (resp.errorCode) {
        console.error(`Error sending action ${action}`, resp.errorCode, resp);
    }
}

export async function connectToGameWebsocket(user: User, websocketBaseUrl: string, urlPath, roomId, key): Promise<void> {
    await ProtoUtil.loadProtobuf();

    currentUser = user;
    GameLogic.setCurrentUserId(user.userId);

    let domain = websocketBaseUrl.split("//")[1].split("/")[0];
    const queryParams = {
        userId: user.userId,
        sessionToken: user.token,
        connectTime: Date.now().toString(),
        roomId,
        key,
        u: encodeURIComponent(btoa(domain))
    }
    const url = `${websocketBaseUrl}${urlPath}/battle/echo?${querystring.stringify(queryParams)}`
    console.log("Connecting to WebSocket", { url });
    client = new WebSocket(url);

    client.onmessage = onMessage;
    client.onerror = (err) => {
        console.error("WebSocket error:", err);
    };
    client.onclose = () => {
        console.log("WebSocket connection closed");
        resolveGameEnd();
    };
    let onConnected: (result?: any) => void;
    client.onopen = () => {
        currentRoomId = roomId;
        console.log("WebSocket connection established");
        onConnected();
    };
    return new Promise((resolve) => {
        onConnected = resolve;
    });
}

function onMessage(event: WebSocket.MessageEvent) {
    const data = event.data as Buffer;
    let resp;
    try {
        resp = ProtoUtil.decode("MsgDeliverResp", decryptAESBytes(data, currentUser.gameAesKey))
    } catch (e) {
        resp = ProtoUtil.decode("MsgDeliverResp", new Uint8Array(data));
    }
    if (resp.msgBody) {
        resp.msgBody = ProtoUtil.decode(MESSAGE_TYPES[resp.msgType], resp.msgBody);
    }
    callbackMap[resp.callbackId]?.callback(resp);
    callbackMap[resp.callbackId] = undefined;

    GameLogic.processGameMessage(resp)
}


function sendWebsocketMessage(msgType, data, callback: CustomWebsocketCallback): void {
    if (!client || client.readyState !== WebSocket.OPEN) {
        throw new Error(`WebSocket is not connected. Cannot send message of type ${msgType}`);
    }

    const callbackId = Math.floor(Math.random() * 1000000).toString();

    let now = Date.now();
    callbackMap[callbackId] = { callback, created_time: now };

    if (now - lastCallbackCacheClean > 60_000) {
        Object.entries(callbackMap).forEach(([id, callbackData]) => {
            if (callbackData && now - callbackData.created_time > 60_000) {
                callbackMap[id] = undefined;
            }
        });
        lastCallbackCacheClean = now;
    }

    const msgBody = data ? ProtoUtil.encode(MESSAGE_TYPES[msgType] || msgType, data) : null;
    const encoded = {
        targetId: currentRoomId,
        stubInfo: {
            userId: currentUser.userId,
            version: "5.8.8.14",
            sessionToken: currentUser.token,
        },
        callbackId,
        msgType,
        msgBody,
    };
    console.log(`WS -> ${msgType}:`, { msgBody: data });
    const message = ProtoUtil.encode("MsgDeliverReq", encoded);
    client.send(message);
}

function sendAsyncWebsocketMessage(msgType, data): Promise<CustomWebsocketResponse> {
    return new Promise((resolve, reject) => {
        sendWebsocketMessage(msgType, data, (resp) => {
            console.log(`WS <- ${msgType}`);
            if (resp.errorCode) {
                console.error(`Error in response for ${msgType}:`, resp.errorCode, resp);
                reject(resp);
            }
            resolve(resp);
        });
    });
}
