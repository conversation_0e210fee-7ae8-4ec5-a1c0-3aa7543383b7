import { logging } from './logging';
import * as pokersolver from 'pokersolver';

export enum HandName {
    HIGH_CARD = 'High Card',
    ONE_PAIR = 'Pair',
    TWO_PAIRS = 'Two Pair',
    THREE_OF_A_KIND = 'Three of a Kind',
    STRAIGHT = 'Straight',
    FLUSH = 'Flush',
    FULL_HOUSE = 'Full House',
    FOUR_OF_A_KIND = 'Four of a Kind',
    STRAIGHT_FLUSH = 'Straight Flush',
}

export interface EvaluatedHand {
    value: number;
    handName: HandName;
}

export function evaluateHand(holeCards: string[], communityCards: string[]): EvaluatedHand {
    try {
        const result = pokersolver.Hand.solve(holeCards.concat(communityCards));
        return {
            value: result.rank ?? 0,
            handName: result.name ?? HandName.HIGH_CARD,
        };
    } catch (error) {
        logging.error('[SHOW_CARDS_FEATURE] - error in evaluateHand', error, {
            holeCards,
            communityCards,
        });
        return {
            value: 0,
            handName: HandName.HIGH_CARD,
        };
    }
}
