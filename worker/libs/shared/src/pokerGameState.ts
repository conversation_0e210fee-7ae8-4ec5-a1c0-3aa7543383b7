import {
    GameStateInterface,
    StrategyResponseAction,
    GamePlayer,
    GameActions,
    GameAction,
    FetchStrategyArgs,
} from './types';
import { GAME_PHASE, GameMode, GameType } from './constants';
import { fetchStrategy } from './strategy';
import { logging } from './logging';

/**
 * State of single poker hand used for fetching strategy
 */
export class GameState {
    gameuuid: string = '';
    game_type: GameType;
    game_mode_code: GameMode;
    roomid: string = '';
    big_blind: number = 0;
    ante: number = 0;
    actions: GameActions;
    players: GamePlayer[];
    dealer_seat: number = -1;
    sb_seat: number = -1;
    bb_seat: number = -1;
    straddle_seat: number = -1;
    post_seats?: number[];

    constructor(
        gameuuid: string,
        roomid: string,
        big_blind: number,
        ante: number,
        gameModeCode: GameMode = GameMode.NORMAL,
        gameTypeCode: GameType = GameType.NLHE,
    ) {
        this.gameuuid = gameuuid;
        this.roomid = roomid;
        this.big_blind = big_blind;
        this.ante = ante || 0;
        this.actions = {
            entries: [],
        };
        this.players = [];
        this.game_mode_code = gameModeCode;
        this.game_type = gameTypeCode;
    }

    addCardsAction(cards: string) {
        this.actions.entries.push({
            action: cards,
        });
    }

    addPlayer(uid: string | undefined, seat_no: number, stack: number): GamePlayer {
        const player = {
            uid,
            seat_no,
            stack,
        };
        this.players.push(player);
        return player;
    }

    addAction(action: StrategyResponseAction | string, amount?: number, seat_no?: number) {
        const gameAction: GameAction = {
            action,
        };
        if (amount) {
            gameAction.amount = amount;
        }
        if (seat_no !== undefined) {
            gameAction.seat_no = seat_no;
        }
        this.actions.entries.push(gameAction);
    }

    getPlayerById(uid: string): GamePlayer | undefined {
        return this.players.find((p) => p.uid === uid);
    }

    getPlayerBySeatNo(seat_no: number): GamePlayer | undefined {
        return this.players.find((p) => p.seat_no === seat_no);
    }

    setPlayerCards(uid: string, cards: string) {
        const player = this.getPlayerById(uid);
        if (!player) {
            logging.warn(`Player with uid was not found`, { uid, players: this.players });
            throw new Error(`Player with uid was not found`);
        }

        player.hole_cards = cards;
    }

    getPlayerCards(uid: string): string[] {
        const player = this.getPlayerById(uid);
        if (!player) {
            logging.warn(`Player with uid was not found`, { uid, players: this.players });
            throw new Error(`Player with uid was not found`);
        }

        if (!player?.hole_cards) {
            logging.warn(`No hole cards found for player with uid`, {
                uid,
                players: this.players,
            });
            throw new Error(`No hole cards found for player with uid`);
        }

        const result = [player.hole_cards.slice(0, 2), player.hole_cards.slice(2, 4)];
        return result; // "8d2s" -> ["8d", "2s"]
    }

    setGameParams({
        gameuuid,
        roomid,
        big_blind,
        ante,
        game_mode_code,
        game_type,
        dealer_seat,
        sb_seat,
        bb_seat,
        straddle_seat,
    }: Partial<GameStateInterface>) {
        if (gameuuid) this.gameuuid = gameuuid;
        if (roomid) this.roomid = roomid;
        if (big_blind) this.big_blind = big_blind;
        if (ante) this.ante = ante;
        if (game_mode_code) this.game_mode_code = game_mode_code;
        if (game_type) this.game_type = game_type;
        if (dealer_seat !== undefined) this.dealer_seat = dealer_seat;
        if (sb_seat !== undefined) this.sb_seat = sb_seat;
        if (bb_seat !== undefined) this.bb_seat = bb_seat;
        if (straddle_seat !== undefined) this.straddle_seat = straddle_seat;
    }

    getCommunityCards(): string[] {
        // the action is a card action if it starts with uppercase letter or a number
        return this.actions.entries
            .filter((a) => /^[A-Z0-9]/.test(a.action))
            .map((a) => a.action)
            .flatMap((c) => (c.length === 6 ? [c.slice(0, 2), c.slice(2, 4), c.slice(4)] : c));
    }

    getGamePhase(): GAME_PHASE {
        const cards = this.getCommunityCards();
        return cards.length;
    }

    playerHasFolded(seat_no: number): boolean {
        return this.actions.entries
            .filter((a) => a.seat_no === seat_no)
            .some((a) => a.action === StrategyResponseAction.FOLD);
    }

    async fetchStrategy(args: Partial<FetchStrategyArgs>): Promise<GameAction> {
        return fetchStrategy({
            state: this,
            ...args,
        });
    }

}
