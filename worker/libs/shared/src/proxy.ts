import { HttpsProxyAgent } from 'https-proxy-agent';
import { JobContext } from './context';
import { logging } from './logging';

let proxyAgent: HttpsProxyAgent<string> | undefined = undefined;

export function getProxyAgent(): HttpsProxyAgent<string> | undefined {
    if (proxyAgent === undefined) {
        const proxyUrl = JobContext.proxyUrl();
        if (proxyUrl) {
            logging.info(`Setting worker proxy agent with URL: ${proxyUrl}`);
            proxyAgent = new HttpsProxyAgent(proxyUrl);
        }
    }
    return proxyAgent;
}
