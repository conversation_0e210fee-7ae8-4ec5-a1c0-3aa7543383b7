import { test } from 'node:test';
import assert from 'node:assert';

import { GameState } from '../src/pokerGameState';
import { StrategyResponseAction } from '../src/types';

const [playerid1, seat_no1, stack1] = ['1', 0, 100];
const [playerid2, seat_no2, stack2] = ['2', 1, 100];

test('GameState - heroHasFolded', async () => {
    const gameState = new GameState('123', '123', 10, 2);

    gameState.addPlayer(playerid1, seat_no1, stack1);
    gameState.addPlayer(playerid2, seat_no2, stack2);

    gameState.addAction(StrategyResponseAction.CALL, 5, seat_no1);
    gameState.addAction(StrategyResponseAction.CHECK, 0, seat_no2);
    gameState.addAction('QcKcTd');

    gameState.addAction(StrategyResponseAction.BET, 20, seat_no1);
    gameState.addAction(StrategyResponseAction.FOLD, 0, seat_no2);

    assert.strictEqual(gameState.playerHasFolded(seat_no1), false);
    assert.strictEqual(gameState.playerHasFolded(seat_no2), true);
});
