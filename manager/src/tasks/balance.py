from datetime import datetime, timedelta, timezone

from beanie.operators import NotIn

from src.db.models import FutureLaunch, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger


async def start_balance_bot(amount: int = 1):
    name = "tasks.start_balance_bot"

    if amount <= 0:
        raise ValueError(f"amount must be greater than 0, got {amount}")

    max_booked_launch_time = datetime.now(timezone.utc) + timedelta(hours=1)
    future_launches = await FutureLaunch.find(
        FutureLaunch.starting_time <= max_booked_launch_time
    ).to_list()
    reserved_players = [launch.player_id for launch in future_launches]

    match_stage = Player.find(
        Player.status == PlayerStatus.IDLE.value,
        Player.need_balance_update == True,  # noqa: E712
        NotIn(Player.player_id, reserved_players),
    ).get_filter_query()

    pipeline = [
        {"$match": match_stage},
        {"$group": {"_id": "$platform_id", "players": {"$push": "$$ROOT"}}},
        {"$project": {"players": {"$slice": ["$players", amount]}}},
    ]

    groups = await Player.aggregate(pipeline).to_list(None)
    if not groups:
        return

    delay = 0
    interval = 1.0 / float(amount)
    # Start a balance bot for up to 'amount' players from each platform
    for doc in groups:
        for raw_player in doc.get("players", []):
            if raw_player is None:
                continue
            player = Player.model_validate(raw_player)

            seat = Seat(player=player, table_id=None)
            try:
                await BotService.start_bot(seat=seat, bot_type=BotType.BALANCE, attempts=2, delay=delay)
                delay += interval
                logger.info(name, f"Started balance bot for player {player.player_id}")
            except ValueError as e:
                logger.error(name, f"Failed to start balance bot for player {player.player_id}", e)
