from datetime import datetime
from typing import List, Literal, Optional, Dict
from pydantic import BaseModel, ConfigDict, model_validator
from pydantic.networks import EmailStr
from src.db.models import Player
from src.schemas.responses import PlayerBalanceResponse, PlayTimeRangeResponse
from src.utils.enums import AppType, CurrencyType, PlatformType, StrategyProfile


class Seat(BaseModel):
    player: Player
    table_id: str | None  # No default on purpose, to ensure it's set explicitly
    ticket_id: str | None = None
    can_reenter_tournament: bool | None = None
    app_ids: List[int] | None = None
    app_id: AppType | None = None
    club_id: int | None = None  # For friends scanning bot

    buy_in_multiplier: Literal[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000] | None = None
    rebuy_enabled: bool | None = None
    rebuy_threshold: int | None = None
    max_rebuy_count: int | None = None

    # auto withdraw/buyout for tables
    withdraw_amount: int | None = None
    withdraw_threshold: int | None = None

    transfer_amount: int | None = None
    receiver_id: int | None = None
    receiver_username: str | None = None
    currency: CurrencyType | None = None
    strategy_profile: StrategyProfile | None = None
    create_room_params: Dict[str, str | int | float | bool] | None = (
        None  # params for friends room creation
    )

    model_config = ConfigDict(use_enum_values=True)


class SeatRequest(BaseModel):
    player_id: str
    table_id: str | None
    ticket_id: str | None = None
    app_ids: List[int] | None = None
    club_id: int | None = None

    buy_in_multiplier: Literal[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000] | None = None
    rebuy_enabled: bool | None = None
    rebuy_threshold: int | None = None
    max_rebuy_count: int | None = None
    attempts: int | None = 3

    withdraw_amount: int | None = None
    withdraw_threshold: int | None = None

    transfer_amount: int | None = None
    receiver_id: int | None = None
    receiver_username: str | None = None
    currency: CurrencyType | None = None
    strategy_profile: StrategyProfile | None = None

    # params for friends room creation
    create_room_params: Dict[str, str | int | float | bool] | None = None


class RebuyConfiguration(BaseModel):
    trigger: int
    rebuy_amount: int


class BotStartRequest(BaseModel):
    seats: List[SeatRequest]
    type: Literal["play", "scan", "balance"] = "play"
    delay_min: Optional[int] = None
    delay_max: Optional[int] = None


class AddUserAccountRequest(BaseModel):
    username: str
    account: Optional[str] = None
    password: Optional[str] = None
    country_code: str
    area_code: Optional[str] = None
    phone_number: Optional[str] = None
    receiver_id: Optional[int] = None
    receiver_username: Optional[str] = None
    email: Optional[EmailStr] = None
    user_id: Optional[str] = None


class RegisterPlayerRequest(AddUserAccountRequest):
    app_id: int
    platform_id: PlatformType

    model_config = ConfigDict(
        use_enum_values=True,
        extra="allow",
    )


class AddMultipleUserAccountsRequest(BaseModel):
    app_id: int
    platform_id: int
    accounts: List[AddUserAccountRequest]


class PlayerUpdateRequest(BaseModel):
    bot_type: str | None = None
    allowed_games: List[AppType] | None = None
    enabled: bool | None = None
    status: str | None = None
    bot_id: str | None = None
    table_id: str | None = None
    balance: PlayerBalanceResponse | None = None
    country_code: str | None = None
    play_time_range: PlayTimeRangeResponse | None = None
    club_ids: list[int] | None = None


class AutoStartConfigRequest(BaseModel):
    app_id: int
    enabled: Optional[bool] = None
    cover_before_start: Optional[float] = None
    cover_late_registration: Optional[float] = None
    bot_min_delay_sec: Optional[int] = None
    bot_max_delay_sec: Optional[int] = None
    schedule_min_players: Optional[int] = None
    check_interval_sec: Optional[int] = None
    check_before_start_min: Optional[int] = None
    min_prize_pool: Optional[int] = None
    reentry_probability: float | None = None


class TournamentConfigUpdateRequest(BaseModel):
    app_id: int
    adjusted_game_pool: Optional[float] = None
    scheduling_min_delay_sec: Optional[int] = None
    scheduling_max_delay_sec: Optional[int] = None

    @model_validator(mode="after")
    def validate_scheduling_range(self):
        if (
            self.scheduling_min_delay_sec is not None
            and self.scheduling_max_delay_sec is not None
            and self.scheduling_min_delay_sec > self.scheduling_max_delay_sec
        ):
            raise ValueError("scheduling_min_delay_sec must be less than scheduling_max_delay_sec")
        return self


class TournamentsConfigUpdateRequest(TournamentConfigUpdateRequest):
    tournament_ids: list[str]


class SignInRequest(BaseModel):
    email: EmailStr
    password: str


class RefreshTokenRequest(BaseModel):
    sub: str
    refresh_token: str


class TournamentCheckStartRequest(BaseModel):
    player_id: str
    tournament_id: str
    app_id: int
    checked: Optional[bool] = False


class TournamentCheckBatchRequest(BaseModel):
    checks: list[TournamentCheckStartRequest]


class AddFutureLaunchRequest(BaseModel):
    player_id: str
    tournament_id: str
    app_id: int
    ticket_id: Optional[str] = None
    starting_time: Optional[datetime] = None


class IPPoolConfigurationCreateRequest(BaseModel):
    full_proxy_url: str
    external_ip: str
    country_code: str


class IPPoolAssignRequest(BaseModel):
    player_id: str


class TablesAutomationConfigRequest(BaseModel):
    config_id: int | None = None
    app_id: int
    club_id: int | None = None
    is_enabled: bool
    start_interval: int
    stop_interval: int
    big_blinds: List[int]
    table_rebuy_threshold: int = 0
    room_modes: List[int]
    players_count: List[int] = [4, 7]
    min_players_count_to_stop: int = 0
    max_players_count_to_stop: int = 9
    ante: int
    currencies: List[CurrencyType] = ["DIAMOND", "GOLD"]
    max_bots_per_table: int = 1
    max_rebuy_count: int = 5
    buy_in_multipliers: List[Literal[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]] = [
        100,
        200,
        300,
    ]
    occupancy_threshold: int
    strategy_profile: Dict[StrategyProfile, int] | None = {}
    tables_affected_percentage: int = 100
    zoom_allowed_bots_percent: int = 0
    zoom_start_bots_per_interval: int = 1

    # auto-withdraw/buyout params
    withdraw_amount: int = 0
    withdraw_threshold: int = 0

    model_config = ConfigDict(
        use_enum_values=True,
    )


class TransferBalanceRequest(BaseModel):
    player_ids: List[str]
    receiver_id: int | None = None
    receiver_username: str | None = None
    keep_amount: int
    currency: CurrencyType


class RegistrationAutomationConfigRequest(BaseModel):
    is_enabled: bool
    min_delay_sec: int
    max_delay_sec: int


class ChangeAvatarRequest(BaseModel):
    avatarBase64: str
    imgExt: str
