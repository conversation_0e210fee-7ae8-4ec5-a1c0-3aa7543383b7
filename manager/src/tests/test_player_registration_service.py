import pytest
from unittest.mock import AsyncMock, patch
from src.db.models import IPPoolConfiguration
from src.services.player_registration_service import PlayerRegistrationService
from src.schemas.requests import RegisterPlayerRequest


register_player_request = RegisterPlayerRequest(
    app_id=1,
    platform_id=98,
    country_code="CN",
    username="testuser",
    account="testaccount",
    password=None,
    area_code="+86",
    phone_number="**********",
    email="<EMAIL>",
    receiver_id=42,
    receiver_username="receiveruser",
    model_extra={}
)


@pytest.mark.asyncio
@patch("src.services.player_registration_service.IPPoolConfigurationService.ip_pool_enabled", return_value=False)
@patch("src.services.player_registration_service.PlayerRegistrationService._register_in_game_app", new_callable=AsyncMock)
@patch("src.services.player_registration_service.PlayerRegistrationService._send_player_to_worker", new_callable=AsyncMock)
async def test_register_player_ip_pool_disabled(
    mock_send_player_to_worker,
    mock_register_in_game_app,
    mock_ip_pool_enabled,
    mock_db,
):

    response = await PlayerRegistrationService().register_player(register_player_request)
    assert response.external_ip is None
    assert response.error is None

    mock_register_in_game_app.assert_awaited()
    mock_send_player_to_worker.assert_awaited()


@pytest.mark.asyncio
@patch("src.services.player_registration_service.IPPoolConfigurationService.get_free_ip", new_callable=AsyncMock)
@patch("src.services.player_registration_service.IPPoolConfigurationService.ip_pool_enabled", return_value=True)
@patch("src.services.player_registration_service.PlayerRegistrationService._register_in_game_app", new_callable=AsyncMock)
@patch("src.services.player_registration_service.PlayerRegistrationService._send_player_to_worker", new_callable=AsyncMock)
async def test_register_player_ip_pool_disabled_raisies(
    mock_send_player_to_worker,
    mock_register_in_game_app,
    mock_ip_pool_enabled,
    mock_get_free_ip,
    mock_db,
):
    mock_get_free_ip.return_value = None

    response = await PlayerRegistrationService().register_player(register_player_request)
    assert response.error is not None
    assert response.external_ip is None

    mock_register_in_game_app.assert_not_awaited()
    mock_send_player_to_worker.assert_not_awaited()


@pytest.mark.asyncio
@patch("src.services.player_registration_service.IPPoolConfigurationService.get_free_ip", new_callable=AsyncMock)
@patch("src.services.player_registration_service.IPPoolConfigurationService.ip_pool_enabled", return_value=True)
@patch("src.services.player_registration_service.PlayerRegistrationService._register_in_game_app", new_callable=AsyncMock)
@patch("src.services.player_registration_service.PlayerRegistrationService._send_player_to_worker", new_callable=AsyncMock)
async def test_register_player_ip_pool_enabled_set(
    mock_send_player_to_worker,
    mock_register_in_game_app,
    mock_ip_pool_enabled,
    mock_get_free_ip,
    mock_db,
):
    mock_get_free_ip.return_value = await IPPoolConfiguration(
        ip_conf_id='ip1',
        external_ip='*******',
        full_proxy_url='http://user:pass@',
        country_code='CN'
    ).save()

    response = await PlayerRegistrationService().register_player(register_player_request)
    assert response.error is None
    assert response.external_ip == "*******"

    mock_register_in_game_app.assert_awaited()
    mock_send_player_to_worker.assert_awaited()
