from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch
from zoneinfo import ZoneInfo

import pytest

from src.db.models import FutureLaunch
from src.tasks.balance import start_balance_bot
from src.utils.enums import PlayerStatus, PlatformType

from src.tests.fixtures import CnP<PERSON> as Player


@patch("src.tasks.balance.BotService")
@pytest.mark.asyncio
async def test_start_balance_bot(mock_bot_service, mock_db):
    mock_bot_service.start_bot = AsyncMock()
    # Preparation - Insert mock tournament data
    await Player(app_id=789, enabled=True, status=PlayerStatus.IDLE.value, player_id="456",
                 platform_id=PlatformType.WPK, need_balance_update=True).insert()
    await Player(app_id=789, enabled=True, status=PlayerStatus.IDLE.value, player_id="123",
                 platform_id=PlatformType.WPK, need_balance_update=True).insert()
    await Player(app_id=789, enabled=True, status=PlayerStatus.IDLE.value, player_id="789",
                 platform_id=PlatformType.WPTGO, need_balance_update=True).insert()

    # Run function
    await start_balance_bot(2)

    assert mock_bot_service.start_bot.await_count == 3


@patch("src.tasks.balance.BotService")
@pytest.mark.asyncio
async def test_start_balance_bot_reserved(mock_bot_service, mock_db):
    mock_bot_service.start_bot = AsyncMock()
    # Preparation - Insert mock tournament data
    test_tournament_id = "123"
    test_player_id = "456"
    test_app_id = 789
    await Player(app_id=test_app_id, enabled=True, status=PlayerStatus.IDLE.value, player_id=test_player_id,
                 need_balance_update=True).insert()
    await FutureLaunch(app_id=test_app_id, player_id=test_player_id, tournament_id=test_tournament_id,
                       starting_time=datetime.now(ZoneInfo("UTC")) + timedelta(minutes=1)).insert()

    # Run function
    await start_balance_bot()

    assert mock_bot_service.start_bot.await_count == 0
