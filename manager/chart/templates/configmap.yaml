{{- $configmap := .Values.configmap | default dict }}
{{- $annotations := $configmap.annotations | default dict }}
{{- $labels := $configmap.labels | default dict }}
{{- $context := dict "name" $configmap.name "annotations" $annotations "labels" $labels }}
apiVersion: v1
kind: ConfigMap
{{- include "library-metadata" (dict "root" $ "context" $context ) }}
data:
  WORKER_URL: "http://botworker.bots.svc.cluster.local:3000"
  WPK_SIGN_SALT: "{{ .Values.configmap.WPK_SIGN_SALT }}"
  WPK_URL: "{{ .Values.configmap.WPK_URL }}"
  ENV: {{ .Values.environment }}
  MONGO_PORT: "27017"
  REDIS_PORT: "6379"
  MONGO_SSL_CERT: ""
  MONGO_SSL_DISABLED: "True"
  IP_POOL_ENABLED: "{{ .Values.configmap.IP_POOL_ENABLED }}"
  MAX_PLAYERS_PER_IP: "{{ .Values.configmap.MAX_PLAYERS_PER_IP }}"
  JOB_STALL_TIMEOUT: "{{ .Values.configmap.JOB_STALL_TIMEOUT }}"
  WPTGO_URL: "{{ .Values.configmap.WPTGO_URL }}"
  UNLEASH_API_URL: "{{ .Values.configmap.UNLEASH_API_URL }}"
  DISABLE_IP_POOL_APP_IDS_CHECK: "{{ .Values.configmap.DISABLE_IP_POOL_APP_IDS_CHECK }}"
  PLATFORMS: {{ toJson .Values.configmap.platforms | quote }}
  TASKS_BALANCE_BOT_RATE: "10"
  TASKS_RESERVED_BOT_LAUNCH_RATE: "4"
  CLUBS: {{ toJson .Values.configmap.CLUBS | quote }}
